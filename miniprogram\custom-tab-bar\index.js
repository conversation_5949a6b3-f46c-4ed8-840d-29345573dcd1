import { getStateManager } from '../utils/stateManager.js'

// TabBar 配置
const TABBAR_CONFIGS = {
  '1': [ // 业主
    {
      pagePath: "/pages/index/index",
      text: "首页",
      icon: "wap-home-o"
    }, {
      pagePath: "/pages/service/index",
      text: "信息公开",
      icon: "bullhorn-o"
    }, {
      pagePath: "/pages/finance/finance",
      text: "收支公示",
      icon: "balance-list-o"
    }, {
      pagePath: "/pages/mine/index",
      text: "我的",
      icon: "user-o"
    }
  ],
  '2': [ // 物业
    {
      pagePath: "/pages/property/dashboard/index",
      text: "工作台",
      icon: "dashboard-o"
    }, {
      pagePath: "/pages/property/orders/index",
      text: "工单",
      icon: "orders-o"
    }, {
      pagePath: "/pages/property/patrol/index",
      text: "巡检",
      icon: "check-o"
    }, {
      pagePath: "/pages/property/profile/index",
      text: "我的",
      icon: "user-o"
    }
  ]
}

Component({
  data: {
    selected: 0,
    list: TABBAR_CONFIGS['1'], // 默认业主配置
    currentUserType: '1' // 缓存当前用户类型
  },

  lifetimes: {
    attached() {
      this.initTabBar()
    }
  },

  methods: {
    onChange(event) {
      const index = event.detail
      const url = this.data.list[index].pagePath
      wx.switchTab({
        url
      })
    },

    /**
     * 初始化TabBar（只在组件创建时调用一次）
     */
    initTabBar() {
      try {
        const stateManager = getStateManager()
        const state = stateManager.getState()
        const userType = state.userType || '1'

        this.updateTabBarConfig(userType)
      } catch (error) {
        console.error('初始化TabBar失败:', error)
      }
    },

    /**
     * 更新TabBar配置（只在用户类型变化时调用）
     */
    updateTabBarConfig(userType) {
      // 只有用户类型变化时才更新配置
      if (this.data.currentUserType === userType) {
        return
      }

      const newList = TABBAR_CONFIGS[userType] || TABBAR_CONFIGS['1']

      this.setData({
        list: newList,
        currentUserType: userType
      })

      console.log('TabBar配置已更新:', { userType, listLength: newList.length })
    },

    /**
     * 根据用户类型更新TabBar配置（兼容旧接口）
     */
    updateTabBarByUserType() {
      try {
        const stateManager = getStateManager()
        const state = stateManager.getState()
        const userType = state.userType || '1'

        this.updateTabBarConfig(userType)
      } catch (error) {
        console.error('更新TabBar配置失败:', error)
      }
    },

    /**
     * 设置当前选中的tab
     */
    setSelected(index) {
      this.setData({
        selected: index
      })
    }
  }
})